#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
from Crypto.Cipher import DES
from Crypto.Util.Padding import unpad

def create_des_key(password):
    """
    将密码转换为8字节的DES密钥
    """
    password_bytes = password.encode('utf-8')
    key = bytearray(8)
    
    for i in range(8):
        key[i] = password_bytes[i % len(password_bytes)]
    
    return bytes(key)

def decrypt_des(encrypted_data, password):
    """
    使用DES解密数据
    """
    key = create_des_key(password)
    iv = bytes(8)  # 全零IV，与C#程序保持一致
    
    cipher = DES.new(key, DES.MODE_CBC, iv)
    decrypted_padded = cipher.decrypt(encrypted_data)
    
    # 移除PKCS7填充
    decrypted_data = unpad(decrypted_padded, DES.block_size)
    
    return decrypted_data.decode('utf-8')

def decrypt_game_data(input_file, output_file, password="10086+."):
    """
    解密游戏数据文件
    """
    try:
        # 读取加密的文件
        with open(input_file, 'rb') as f:
            encrypted_data = f.read()
        
        print(f"正在解密文件: {input_file}")
        
        # 解密数据
        decrypted_json = decrypt_des(encrypted_data, password)
        
        # 验证JSON格式
        json_data = json.loads(decrypted_json)
        
        # 保存解密后的JSON文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)
        
        print(f"解密成功！文件已保存为: {output_file}")
        print(f"共解密了 {len(json_data)} 个应用的数据")
        
        return True
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {input_file}")
        return False
    except Exception as e:
        print(f"解密失败: {str(e)}")
        return False

def main():
    """
    主函数
    """
    print("=== 游戏数据解密工具 ===")
    print("默认密码: 10086+.")
    print()
    
    # 默认文件名
    input_file = "游戏数据.json"
    output_file = "游戏数据_解密.json"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"未找到默认文件 {input_file}")
        input_file = input("请输入要解密的文件路径: ").strip()
        
        if not os.path.exists(input_file):
            print("文件不存在，程序退出。")
            return
    
    # 询问输出文件名
    custom_output = input(f"输出文件名 (默认: {output_file}): ").strip()
    if custom_output:
        output_file = custom_output
    
    # 询问密码
    custom_password = input("密码 (默认: 10086+.): ").strip()
    password = custom_password if custom_password else "10086+."
    
    # 执行解密
    success = decrypt_game_data(input_file, output_file, password)
    
    if success:
        print("\n解密完成！")
    else:
        print("\n解密失败！")
    
    input("按回车键退出...")

if __name__ == "__main__":
    main()
