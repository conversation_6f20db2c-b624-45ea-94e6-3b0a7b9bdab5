﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using Microsoft.Win32;
using ValveKeyValue;

namespace DataProcessor;

class Program
{
    static int Main(string[] args)
    {
        if (args.Length > 0)
        {
            using var stream = File.OpenRead(args[0]);

            // Use the second byte to check the type of file
            stream.ReadByte();
            var b = stream.ReadByte();
            stream.Position = 0;

            if (b == 0x44)
            {
                DumpAppInfo(stream);
            }
            else
            {
                return 1;
            }

            return 0;
        }

        var steamLocation = GetSteamPath();

        if (steamLocation == null)
        {
            return 1;
        }

        using (var stream = File.OpenRead(Path.Join(steamLocation, "appcache", "appinfo.vdf")))
        {
            DumpAppInfo(stream);
        }

        return 0;
    }

    private static void DumpAppInfo(FileStream inputStream)
    {
        var appInfo = new AppInfo();
        appInfo.Read(inputStream);

        var apps = new Dictionary<string, object>();

        foreach (var app in appInfo.Apps)
        {
            var appData = new Dictionary<string, object>
            {
                ["_token"] = app.Token,
                ["_changenumber"] = app.ChangeNumber,
                ["_updated"] = app.LastUpdated.ToString("s"),
                ["_hash"] = Convert.ToHexString([.. app.Hash])
            };

            // 将KVObject数据转换为字典
            if (app.Data?.Value != null)
            {
                var convertedData = ConvertKVObjectToDict(app.Data);
                foreach (var kvp in convertedData)
                {
                    appData[kvp.Key] = kvp.Value;
                }
            }

            apps[$"app_{app.AppID}"] = appData;
        }

        var json = JsonSerializer.Serialize(apps, new JsonSerializerOptions
        {
            WriteIndented = true,
            Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
        });

        // 使用DES加密JSON内容
        var encryptedData = EncryptDES(json, "10086+.");
        File.WriteAllBytes("游戏数据.json", encryptedData);
    }



    private static Dictionary<string, object> ConvertKVObjectToDict(KVObject kvObject)
    {
        var result = new Dictionary<string, object>();

        if (kvObject != null)
        {
            foreach (var child in kvObject)
            {
                result[child.Name] = ConvertKVValue(child);
            }
        }

        return result;
    }

    private static object ConvertKVValue(KVObject kvObject)
    {
        if (kvObject.Value != null)
        {
            // 检查是否有子项（嵌套对象）
            if (kvObject.Any())
            {
                // 如果是嵌套对象，递归转换
                var nestedDict = new Dictionary<string, object>();
                foreach (var child in kvObject)
                {
                    nestedDict[child.Name] = ConvertKVValue(child);
                }
                return nestedDict;
            }
            else
            {
                // 如果是基本值，直接返回
                return kvObject.Value.ToString();
            }
        }

        return null;
    }

    private static byte[] EncryptDES(string plainText, string password)
    {
        byte[] plainBytes = Encoding.UTF8.GetBytes(plainText);

        // 创建DES密钥和IV
        byte[] key = CreateDESKey(password);
        byte[] iv = new byte[8]; // DES使用8字节IV，这里使用全零IV

        using (var des = DES.Create())
        {
            des.Key = key;
            des.IV = iv;
            des.Mode = CipherMode.CBC;
            des.Padding = PaddingMode.PKCS7;

            using (var encryptor = des.CreateEncryptor())
            using (var msEncrypt = new MemoryStream())
            using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
            {
                csEncrypt.Write(plainBytes, 0, plainBytes.Length);
                csEncrypt.FlushFinalBlock();
                return msEncrypt.ToArray();
            }
        }
    }

    private static byte[] CreateDESKey(string password)
    {
        // 将密码转换为8字节的DES密钥
        byte[] passwordBytes = Encoding.UTF8.GetBytes(password);
        byte[] key = new byte[8];

        for (int i = 0; i < key.Length; i++)
        {
            key[i] = passwordBytes[i % passwordBytes.Length];
        }

        return key;
    }

    private static string GetSteamPath()
    {
        if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
        {
            var key = Registry.CurrentUser.OpenSubKey("SOFTWARE\\Valve\\Steam") ??
                      RegistryKey.OpenBaseKey(RegistryHive.LocalMachine, RegistryView.Registry64)
                          .OpenSubKey("SOFTWARE\\Valve\\Steam");

            if (key != null && key.GetValue("SteamPath") is string steamPath)
            {
                return steamPath;
            }
        }
        else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
        {
            var home = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);
            var paths = new[] { ".steam", ".steam/steam", ".steam/root", ".local/share/Steam" };

            return paths
                .Select(path => Path.Join(home, path))
                .FirstOrDefault(steamPath => Directory.Exists(Path.Join(steamPath, "appcache")));
        }
        else if (OperatingSystem.IsMacOS())
        {
            var home = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            return Path.Join(home, "Steam");
        }

        throw new PlatformNotSupportedException();
    }
}
