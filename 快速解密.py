#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
import sys
from Crypto.Cipher import DES
from Crypto.Util.Padding import unpad

def create_des_key(password):
    """将密码转换为8字节的DES密钥"""
    password_bytes = password.encode('utf-8')
    key = bytearray(8)
    
    for i in range(8):
        key[i] = password_bytes[i % len(password_bytes)]
    
    return bytes(key)

def decrypt_des(encrypted_data, password):
    """使用DES解密数据"""
    key = create_des_key(password)
    iv = bytes(8)  # 全零IV
    
    cipher = DES.new(key, DES.MODE_CBC, iv)
    decrypted_padded = cipher.decrypt(encrypted_data)
    decrypted_data = unpad(decrypted_padded, DES.block_size)
    
    return decrypted_data.decode('utf-8')

def main():
    """主函数 - 直接解密游戏数据.json文件"""
    input_file = "游戏数据.json"
    output_file = "游戏数据_解密.json"
    password = "10086+."
    
    if not os.path.exists(input_file):
        print(f"错误: 找不到文件 {input_file}")
        return
    
    try:
        print("正在解密游戏数据...")
        
        # 读取加密文件
        with open(input_file, 'rb') as f:
            encrypted_data = f.read()
        
        # 解密数据
        decrypted_json = decrypt_des(encrypted_data, password)
        json_data = json.loads(decrypted_json)
        
        # 保存解密后的文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)
        
        print(f"解密成功！")
        print(f"输入文件: {input_file}")
        print(f"输出文件: {output_file}")
        print(f"应用数量: {len(json_data)}")
        
    except Exception as e:
        print(f"解密失败: {str(e)}")

if __name__ == "__main__":
    main()
